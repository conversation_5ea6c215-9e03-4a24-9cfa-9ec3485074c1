package com.yx.ikun.block.custom;

import com.yx.ikun.block.ModBlock;
import net.minecraft.core.BlockPos;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.level.Level;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;

public class ElevatorBlock extends Block {
    @Override
    public void stepOn(Level level, BlockPos blockPos, BlockState blockState, Entity entity) {
        if (entity instanceof LivingEntity livingEntity) {
            if (livingEntity.isShiftKeyDown()) {
                for (int i = blockPos.getY() - 3; i >= blockPos.getY() - 20; i--) {
                    if (blockState.getBlock().getName().getString()
                            .equals(ModBlock.ELEVATOR_BLOCK.get().getName().getString())) {
                        livingEntity.setPos(livingEntity.getX(),
                                i + 1,
                                livingEntity.getZ());
                        break;
                    }
                }
            }
        }
        super.stepOn(level, blockPos, blockState, entity);
    }
    public ElevatorBlock(Properties properties) {
        super(properties);
    }
}

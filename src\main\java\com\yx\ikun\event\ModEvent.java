package com.yx.ikun.event;

import com.yx.ikun.Ikun;
import com.yx.ikun.sound.ModSound;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundSource;
import net.minecraft.world.entity.animal.Chicken;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber(modid = Ikun.MODID)
public class ModEvent {
    @SubscribeEvent
    public static void chickenAttack(LivingHurtEvent event) {
        if (event.getEntity() instanceof Chicken) {
            if (event.getSource().getEntity() instanceof Player player) {
                player.sendSystemMessage(Component.literal("你干嘛~哎呦~"));
                player.playSound(ModSound.IKUN_NGM.get(), 1.0F, 1.0F);
            }
        }
    }
}

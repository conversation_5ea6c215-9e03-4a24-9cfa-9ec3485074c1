package com.yx.ikun.sound;

import com.yx.ikun.Ikun;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class ModSound {
    public static final DeferredRegister<SoundEvent> SOUND_EVENTS = DeferredRegister
            .create(ForgeRegistries.SOUND_EVENTS, Ikun.MODID);
    public static RegistryObject<SoundEvent> IKUN_NGM = registerSoundEvent("ikun_ngm");

    public static final RegistryObject<SoundEvent> DOWSING_ROD_FOUND_ORE =
            registerSoundEvent("dowsing_rod_found_ore");

    public static RegistryObject<SoundEvent> IKUN_MUSIC_ONE = registerSoundEvent("ikun_music_one");
    public static RegistryObject<SoundEvent> IKUN_MUSIC_TWO = registerSoundEvent("ikun_music_two");
    public static RegistryObject<SoundEvent> IKUN_MUSIC_THREE = registerSoundEvent("ikun_music_three");
    public static RegistryObject<SoundEvent> IKUN_MUSIC_FOUR = registerSoundEvent("ikun_music_four");
    public static RegistryObject<SoundEvent> IKUN_MUSIC_FIVE = registerSoundEvent("ikun_music_five");
    public static RegistryObject<SoundEvent> IKUN_MUSIC_SIX = registerSoundEvent("ikun_music_six");

    private static RegistryObject<SoundEvent> registerSoundEvent(String name) {
        return SOUND_EVENTS.register(name, () -> new SoundEvent(new ResourceLocation(Ikun.MODID, name)));
    }

    public static void register(IEventBus eventBus) {
        SOUND_EVENTS.register(eventBus);
    }
}

package com.yx.ikun.item;

import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.food.FoodProperties;

public class ModFood {
    public static final FoodProperties FRIED_EGGS = (new FoodProperties.Builder())
            .nutrition(4)
            .saturationMod(4f)
            .effect(() -> new MobEffectInstance(MobEffects.ABSORPTION, 20 * 60 * 3, 1), 1)
            .alwaysEat()
            .build();
}

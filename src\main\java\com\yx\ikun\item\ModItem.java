package com.yx.ikun.item;

import com.yx.ikun.Ikun;
import com.yx.ikun.item.custom.IkunCoalItem;
import com.yx.ikun.item.custom.MineralDetectorItem;
import com.yx.ikun.item.custom.ModArmorItem;
import com.yx.ikun.sound.ModSound;
import net.minecraft.world.effect.MobEffectInstance;
import net.minecraft.world.effect.MobEffects;
import net.minecraft.world.entity.EquipmentSlot;
import net.minecraft.world.food.FoodProperties;
import net.minecraft.world.item.*;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

/**
 * <AUTHOR>
 */

public class ModItem {
    public static final DeferredRegister<Item> ITEMS =
            DeferredRegister.create(ForgeRegistries.ITEMS, Ikun.MODID);

    public static final RegistryObject<Item> FRIED_EGGS = ITEMS.register("fried_eggs",
            () -> new Item(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).food(ModFood.FRIED_EGGS)));

    public static final RegistryObject<Item> RAW_IKUN = ITEMS.register("raw_ikun",
            () -> new Item(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN = ITEMS.register("ikun",
            () -> new Item(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_NUGGET = ITEMS.register("ikun_nugget",
            () -> new Item(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> MINERAL_DETECTOR = ITEMS.register("mineral_detector",
            () -> new MineralDetectorItem(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).durability(16)));

    public static final RegistryObject<Item> IKUN_SWORD = ITEMS.register("ikun_sword",
            () -> new SwordItem(ModTiers.IKUN, 4, -2f,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_PICKAXE = ITEMS.register("ikun_pickaxe",
            () -> new PickaxeItem(ModTiers.IKUN, 2, -2f,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_AXE = ITEMS.register("ikun_axe",
            () -> new AxeItem(ModTiers.IKUN, 6, -1.5f,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_SHOVEL = ITEMS.register("ikun_shovel",
            () -> new ShovelItem(ModTiers.IKUN, 4, 1f,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_HOE = ITEMS.register("ikun_hoe",
            () -> new HoeItem(ModTiers.IKUN, -3, -2f,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_MUSIC_ONE_DISC = ITEMS.register("ikun_music_one_disc",
            () -> new RecordItem(4, ModSound.IKUN_MUSIC_ONE,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).stacksTo(1)));
    public static final RegistryObject<Item> IKUN_MUSIC_TWO_DISC = ITEMS.register("ikun_music_two_disc",
            () -> new RecordItem(4, ModSound.IKUN_MUSIC_TWO,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).stacksTo(1)));
    public static final RegistryObject<Item> IKUN_MUSIC_THREE_DISC = ITEMS.register("ikun_music_three_disc",
            () -> new RecordItem(4, ModSound.IKUN_MUSIC_THREE,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).stacksTo(1)));
    public static final RegistryObject<Item> IKUN_MUSIC_FOUR_DISC = ITEMS.register("ikun_music_four_disc",
            () -> new RecordItem(4, ModSound.IKUN_MUSIC_FOUR,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).stacksTo(1)));
    public static final RegistryObject<Item> IKUN_MUSIC_FIVE_DISC = ITEMS.register("ikun_music_five_disc",
            () -> new RecordItem(4, ModSound.IKUN_MUSIC_FIVE,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB).stacksTo(1)));

    public static final RegistryObject<Item> IKUN_COAL = ITEMS.register("ikun_coal",
            () -> new IkunCoalItem(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_HELMET = ITEMS.register("ikun_helmet",
            () -> new ModArmorItem(ModArmorMaterials.IKUN, EquipmentSlot.HEAD,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));
    public static final RegistryObject<Item> IKUN_CHESTPLATE = ITEMS.register("ikun_chestplate",
            () -> new ModArmorItem(ModArmorMaterials.IKUN, EquipmentSlot.CHEST,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_LEGGINGS = ITEMS.register("ikun_leggings",
            () -> new ModArmorItem(ModArmorMaterials.IKUN, EquipmentSlot.LEGS,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> IKUN_BOOTS = ITEMS.register("ikun_boots",
            () -> new ModArmorItem(ModArmorMaterials.IKUN, EquipmentSlot.FEET,
                    new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static final RegistryObject<Item> MAGIC_DUST = ITEMS.register("magic_dust",
            () -> new Item(new Item.Properties().tab(ModCreativeModeTab.IKUN_TAB)));

    public static void register(IEventBus eventBus) {
        ITEMS.register(eventBus);
    }
}

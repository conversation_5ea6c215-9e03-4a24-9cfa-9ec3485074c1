package com.yx.ikun.painting;

import com.yx.ikun.Ikun;
import net.minecraft.world.entity.decoration.PaintingVariant;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;

public class ModPaintings {
    public static final DeferredRegister<PaintingVariant> PAINTING_VARIANTS =
            DeferredRegister.create(ForgeRegistries.PAINTING_VARIANTS, Ikun.MODID);

    public static final RegistryObject<PaintingVariant> IKUN1 = PAINTING_VARIANTS.register("ikun1",
            ()-> new PaintingVariant(32,32));
    public static final RegistryObject<PaintingVariant> IKUN2 = PAINTING_VARIANTS.register("ikun2",
            ()->new PaintingVariant(32,32));
    public static final RegistryObject<PaintingVariant> IKUN3 = PAINTING_VARIANTS.register("ikun3",
            ()->new PaintingVariant(32,32));
    public static final RegistryObject<PaintingVariant> IKUN4 = PAINTING_VARIANTS.register("ikun4",
            ()->new PaintingVariant(32,32));
    public static final RegistryObject<PaintingVariant> IKUN5 = PAINTING_VARIANTS.register("ikun5",
            ()->new PaintingVariant(32,32));
    public static final RegistryObject<PaintingVariant> IKUN6 = PAINTING_VARIANTS.register("ikun6",
            ()->new PaintingVariant(32,16));
    public static final RegistryObject<PaintingVariant> IKUN7 = PAINTING_VARIANTS.register("ikun7",
            ()->new PaintingVariant(32,16));
    public static final RegistryObject<PaintingVariant> IKUN8 = PAINTING_VARIANTS.register("ikun8",
            ()->new PaintingVariant(32,32));

    public static void register(IEventBus eventBus){
        PAINTING_VARIANTS.register(eventBus);
    }
}
